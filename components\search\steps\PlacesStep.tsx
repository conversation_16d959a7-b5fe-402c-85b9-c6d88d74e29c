"use client";

import React, { useState, use<PERSON><PERSON>back, useEffect } from "react";
import { PlacesStepProps, PlaceData } from "@/types/search";
import {
  SearchStepContainer,
  SearchStepContent,
  SearchStepActions,
} from "../SearchStepContainer";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MapPin, Search, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface GooglePlacesAutocompleteResult {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
}

const POPULAR_CITIES = [
  { name: "Nearby", value: "nearby" },
  {
    name: "New York City",
    value: "nyc",
    coordinates: { lat: 40.7128, lng: -74.006 },
  },
  {
    name: "Los Angeles",
    value: "la",
    coordinates: { lat: 34.0522, lng: -118.2437 },
  },
  {
    name: "Chicago",
    value: "chicago",
    coordinates: { lat: 41.8781, lng: -87.6298 },
  },
  {
    name: "Miami",
    value: "miami",
    coordinates: { lat: 25.7617, lng: -80.1918 },
  },
  {
    name: "Seattle",
    value: "seattle",
    coordinates: { lat: 47.6062, lng: -122.3321 },
  },
  {
    name: "Boston",
    value: "boston",
    coordinates: { lat: 42.3601, lng: -71.0589 },
  },
];

export const PlacesStep: React.FC<PlacesStepProps> = ({
  value,
  onChange,
  onNext,
  popularCities = POPULAR_CITIES.map((city) => city.name),
  disabled = false,
}) => {
  const [isCustomInput, setIsCustomInput] = useState(value.isCustom);
  const [searchQuery, setSearchQuery] = useState(value.address || "");
  const [predictions, setPredictions] = useState<
    GooglePlacesAutocompleteResult[]
  >([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>("");
  const [showPredictions, setShowPredictions] = useState(false);

  // Initialize Google Places service (mock implementation for now)
  const searchPlaces = useCallback(async (query: string) => {
    if (!query.trim() || query.length < 3) {
      setPredictions([]);
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Mock implementation - in real app, this would use Google Places API
      // For now, we'll simulate some results
      const mockResults: GooglePlacesAutocompleteResult[] = [
        {
          place_id: "mock_1",
          description: `${query} Main Street, New York, NY, USA`,
          structured_formatting: {
            main_text: `${query} Main Street`,
            secondary_text: "New York, NY, USA",
          },
        },
        {
          place_id: "mock_2",
          description: `${query} Avenue, Los Angeles, CA, USA`,
          structured_formatting: {
            main_text: `${query} Avenue`,
            secondary_text: "Los Angeles, CA, USA",
          },
        },
      ];

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 300));
      setPredictions(mockResults);
    } catch (err) {
      setError("Failed to search places. Please try again.");
      setPredictions([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Debounced search
  useEffect(() => {
    if (!isCustomInput) return;

    const timeoutId = setTimeout(() => {
      searchPlaces(searchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, isCustomInput, searchPlaces]);

  const handlePopularCitySelect = (cityValue: string) => {
    const city = POPULAR_CITIES.find((c) => c.value === cityValue);
    if (!city) return;

    const newPlaceData: PlaceData = {
      address: city.name,
      lat: city.coordinates?.lat,
      lng: city.coordinates?.lng,
      placeId: undefined,
      isCustom: false,
      type: cityValue === "nearby" ? "nearby" : "popular",
    };

    onChange(newPlaceData);
    setIsCustomInput(false);
    setSearchQuery("");
    setShowPredictions(false);
  };

  const handleCustomPlaceSelect = (place: GooglePlacesAutocompleteResult) => {
    // In real implementation, you'd get place details including coordinates
    const newPlaceData: PlaceData = {
      address: place.description,
      lat: 40.7128, // Mock coordinates - would come from place details API
      lng: -74.006,
      placeId: place.place_id,
      isCustom: true,
      type: "custom",
    };

    onChange(newPlaceData);
    setSearchQuery(place.description);
    setShowPredictions(false);
  };

  const handleCustomInputChange = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      setShowPredictions(true);
    } else {
      setShowPredictions(false);
      // Clear the current selection if input is cleared
      onChange({
        address: "",
        lat: undefined,
        lng: undefined,
        placeId: undefined,
        isCustom: true,
        type: "custom",
      });
    }
  };

  const toggleCustomInput = () => {
    const newIsCustom = !isCustomInput;
    setIsCustomInput(newIsCustom);

    if (!newIsCustom) {
      // Reset to default when switching back to dropdown
      setSearchQuery("");
      setShowPredictions(false);
      onChange({
        address: "",
        lat: undefined,
        lng: undefined,
        placeId: undefined,
        isCustom: false,
        type: "nearby",
      });
    }
  };

  const canProceed = value.address !== "" || value.type === "nearby";

  return (
    <SearchStepContent>
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <MapPin className="w-5 h-5 text-blue-500" />
          <div>
            <h4 className="font-medium text-gray-900">
              Where are you looking?
            </h4>
            <p className="text-sm text-gray-600">
              Choose a popular city or enter a specific location
            </p>
          </div>
        </div>

        {/* Popular Cities Dropdown */}
        {!isCustomInput && (
          <div className="space-y-2">
            <Label htmlFor="city-select">Popular locations</Label>
            <Select
              value={
                value.type === "nearby" && !value.address
                  ? "nearby"
                  : value.address
              }
              onValueChange={handlePopularCitySelect}
              disabled={disabled}
            >
              <SelectTrigger id="city-select" className="w-full">
                <SelectValue placeholder="Select a location" />
              </SelectTrigger>
              <SelectContent>
                {POPULAR_CITIES.map((city) => (
                  <SelectItem key={city.value} value={city.value}>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      {city.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Custom Location Input */}
        {isCustomInput && (
          <div className="space-y-2">
            <Label htmlFor="location-search">Enter location</Label>
            <div className="relative">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="location-search"
                  type="text"
                  placeholder="Enter address, neighborhood, or city"
                  value={searchQuery}
                  onChange={(e) => handleCustomInputChange(e.target.value)}
                  onFocus={() => setShowPredictions(true)}
                  disabled={disabled}
                  className="pl-10"
                  aria-describedby={error ? "location-error" : undefined}
                  aria-expanded={showPredictions}
                  aria-haspopup="listbox"
                />
                {isLoading && (
                  <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 animate-spin text-gray-400" />
                )}
              </div>

              {/* Predictions Dropdown */}
              {showPredictions && searchQuery.length >= 3 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg">
                  <Command className="border-0">
                    <CommandList className="max-h-48">
                      {isLoading ? (
                        <div className="flex items-center justify-center py-4">
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span className="ml-2 text-sm text-gray-600">
                            Searching...
                          </span>
                        </div>
                      ) : predictions.length > 0 ? (
                        <CommandGroup>
                          {predictions.map((place) => (
                            <CommandItem
                              key={place.place_id}
                              onSelect={() => handleCustomPlaceSelect(place)}
                              className="cursor-pointer"
                            >
                              <div className="flex items-center gap-2">
                                <MapPin className="w-4 h-4 text-gray-400" />
                                <div className="flex flex-col">
                                  <span className="text-sm font-medium">
                                    {place.structured_formatting.main_text}
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    {place.structured_formatting.secondary_text}
                                  </span>
                                </div>
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      ) : (
                        <CommandEmpty>No locations found.</CommandEmpty>
                      )}
                    </CommandList>
                  </Command>
                </div>
              )}
            </div>

            {error && (
              <p
                id="location-error"
                className="text-sm text-red-600"
                role="alert"
              >
                {error}
              </p>
            )}
          </div>
        )}

        {/* Toggle Button */}
        <div className="flex justify-center pt-2">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={toggleCustomInput}
            disabled={disabled}
            className="text-blue-600 hover:text-blue-700"
          >
            {isCustomInput
              ? "Choose from popular cities"
              : "Enter custom location"}
          </Button>
        </div>

        {/* Current Selection Summary */}
        {value.address && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-blue-900">
                  Selected location:
                </p>
                <p className="text-sm text-blue-700">{value.address}</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <SearchStepActions>
        <Button
          onClick={onNext}
          disabled={!canProceed || disabled}
          className="px-6"
        >
          Continue
        </Button>
      </SearchStepActions>
    </SearchStepContent>
  );
};

export default PlacesStep;
