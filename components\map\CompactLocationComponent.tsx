"use client";
import React, { useState, useEffect, useRef } from "react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import <PERSON>ript from "next/script";
import { UseFormReturn } from "react-hook-form";

// Define types for Google Maps objects
// Using the same global declaration as in location.tsx
// The google maps types are defined in the location.tsx file

namespace google.maps {
  interface Map {
    panTo(latLng: LatLngLiteral): void;
  }

  interface Marker {
    setPosition(latLng: LatLngLiteral): void;
    getPosition(): LatLng;
  }

  interface LatLng {
    lat(): number;
    lng(): number;
  }

  interface LatLngLiteral {
    lat: number;
    lng: number;
  }

  interface MapOptions {
    center: LatLngLiteral;
    zoom: number;
    mapTypeId: string;
    mapTypeControl: boolean;
    streetViewControl: boolean;
    fullscreenControl: boolean;
  }

  interface MarkerOptions {
    position: LatLngLiteral;
    map: Map;
    draggable: boolean;
  }

  interface Geocoder {
    geocode(
      request: { location: LatLngLiteral } | { address: string },
      callback?: Function
    ): Promise<GeocoderResponse>;
  }

  interface GeocoderResponse {
    results: GeocoderResult[];
    status: string;
  }

  interface GeocoderResult {
    address_components: GeocoderAddressComponent[];
    formatted_address: string;
    geometry: {
      location: LatLng;
    };
  }

  interface GeocoderAddressComponent {
    long_name: string;
    short_name: string;
    types: string[];
  }
}

interface AddressFormValues {
  address: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
    latitude?: number;
    longitude?: number;
  };
}

interface CompactLocationComponentProps {
  form: UseFormReturn<any>;
  googleMapsApiKey?: string;
  isEditing?: boolean;
}

const CompactLocationComponent: React.FC<CompactLocationComponentProps> = ({
  form,
  googleMapsApiKey = "YOUR_API_KEY",
  isEditing = true,
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<any>(null);
  const markerRef = useRef<any>(null);
  const geocoderRef = useRef<any>(null);

  const [mapsLoaded, setMapsLoaded] = useState<boolean>(false);
  const [isGeocodingLoading, setIsGeocodingLoading] = useState<boolean>(false);
  const [position, setPosition] = useState<{ lat: number; lng: number }>({
    lat: 39.7392,
    lng: -104.9903,
  });

  // Set initial position from form values if available
  useEffect(() => {
    const lat = form.getValues("address.latitude");
    const lng = form.getValues("address.longitude");
    if (lat && lng) {
      setPosition({ lat, lng });
    }
  }, [form]);

  // Handle Google Maps script load
  const handleMapsLoad = () => {
    console.log("Google Maps script loaded");
    if (window.google && window.google.maps) {
      geocoderRef.current = new window.google.maps.Geocoder();
      initializeMap();
      setMapsLoaded(true);
    }
  };

  // Initialize map
  const initializeMap = () => {
    if (!mapRef.current || !window.google || !window.google.maps) return;

    try {
      // Create the map
      const map = new window.google.maps.Map(mapRef.current, {
        center: { lat: position.lat, lng: position.lng },
        zoom: 13,
        mapTypeId: window.google.maps.MapTypeId.ROADMAP,
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: false,
      });
      googleMapRef.current = map;

      // Create the marker
      const marker = new window.google.maps.Marker({
        position: { lat: position.lat, lng: position.lng },
        map: map,
        draggable: isEditing,
      });
      markerRef.current = marker;

      if (isEditing) {
        // Add drag event handlers
        window.google.maps.event.addListener(marker, "dragend", () => {
          const markerPosition = marker.getPosition();
          if (markerPosition) {
            const newPosition = {
              lat: markerPosition.lat(),
              lng: markerPosition.lng(),
            };
            setPosition(newPosition);
            fetchAddressFromCoordinates(newPosition.lat, newPosition.lng);
          }
        });

        // Add click handler to map
        window.google.maps.event.addListener(map, "click", (event: any) => {
          if (event.latLng) {
            const clickPos = {
              lat: event.latLng.lat(),
              lng: event.latLng.lng(),
            };
            setPosition(clickPos);
            marker.setPosition(clickPos);
            fetchAddressFromCoordinates(clickPos.lat, clickPos.lng);
          }
        });
      }
    } catch (error) {
      console.error("Error initializing map:", error);
    }
  };

  // Get current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (pos) => {
          const { latitude, longitude } = pos.coords;
          setPosition({
            lat: latitude,
            lng: longitude,
          });
          if (geocoderRef.current) {
            fetchAddressFromCoordinates(latitude, longitude);
          }
        },
        (error) => {
          console.error("Geolocation error:", error);
        }
      );
    }
  };

  // Fetch address from coordinates
  const fetchAddressFromCoordinates = async (lat: number, lng: number) => {
    if (!geocoderRef.current) return;

    try {
      setIsGeocodingLoading(true);
      const result = await geocoderRef.current.geocode({
        location: { lat, lng },
      });

      if (result.results && result.results.length > 0) {
        const addressComponents = result.results[0].address_components;

        const streetNumber =
          addressComponents.find((comp: any) =>
            comp.types.includes("street_number")
          )?.long_name || "";

        const route =
          addressComponents.find((comp: any) => comp.types.includes("route"))
            ?.long_name || "";

        const city =
          addressComponents.find(
            (comp: any) =>
              comp.types.includes("locality") ||
              comp.types.includes("sublocality")
          )?.long_name || "";

        const state =
          addressComponents.find((comp: any) =>
            comp.types.includes("administrative_area_level_1")
          )?.long_name || "";

        const country =
          addressComponents.find((comp: any) => comp.types.includes("country"))
            ?.long_name || "";

        const postalCode =
          addressComponents.find((comp: any) =>
            comp.types.includes("postal_code")
          )?.long_name || "";

        if (isEditing) {
          // Update form values only when editing
          form.setValue(
            "address.street",
            streetNumber ? `${streetNumber} ${route}` : route
          );
          form.setValue("address.city", city);
          form.setValue("address.state", state);
          form.setValue("address.country", country);
          form.setValue("address.postalCode", postalCode.substring(0, 5));
          form.setValue("address.latitude", lat);
          form.setValue("address.longitude", lng);
        }
      }
    } catch (error) {
      console.error("Geocoding error:", error);
    } finally {
      setIsGeocodingLoading(false);
    }
  };

  // Update map when position changes
  useEffect(() => {
    if (mapsLoaded && googleMapRef.current && markerRef.current) {
      googleMapRef.current.panTo({ lat: position.lat, lng: position.lng });
      markerRef.current.setPosition({ lat: position.lat, lng: position.lng });
    }
  }, [position, mapsLoaded]);

  return (
    <div className="space-y-4">
      <Script
        src={`https://maps.googleapis.com/maps/api/js?key=${googleMapsApiKey}&libraries=places&callback=initMap`}
        onLoad={handleMapsLoad}
        strategy="afterInteractive"
      />

      <Script id="google-maps-init" strategy="afterInteractive">
        {`
          window.initMap = function() {
            console.log("Google Maps initialized via callback");
          }
        `}
      </Script>

      <div className="relative rounded-lg overflow-hidden">
        <div
          ref={mapRef}
          className="h-36 w-full rounded-lg"
          style={{ background: "#f0f0f0" }}
        />

        {/* Current Location Button - Only show when editing */}
        {isEditing && (
          <button
            type="button"
            onClick={getCurrentLocation}
            className="absolute bottom-2 right-2 bg-white p-2 rounded-full shadow-md hover:bg-gray-100"
            title="Use my current location"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="3"></circle>
            </svg>
          </button>
        )}

        {!mapsLoaded && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-200 bg-opacity-50">
            <p>Loading map...</p>
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-3">
        <FormField
          control={form.control}
          name="address.street"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Street</FormLabel>
              <FormControl>
                <Input
                  placeholder="Street address"
                  {...field}
                  disabled={!isEditing}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="address.city"
          render={({ field }) => (
            <FormItem>
              <FormLabel>City</FormLabel>
              <FormControl>
                <Input placeholder="City" {...field} disabled={!isEditing} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-3 gap-3">
        <FormField
          control={form.control}
          name="address.state"
          render={({ field }) => (
            <FormItem>
              <FormLabel>State</FormLabel>
              <FormControl>
                <Input placeholder="State" {...field} disabled={!isEditing} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="address.postalCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Zip Code</FormLabel>
              <FormControl>
                <Input placeholder="12345" {...field} disabled={!isEditing} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="address.country"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Country</FormLabel>
              <FormControl>
                <Input placeholder="Country" {...field} disabled={!isEditing} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

export default CompactLocationComponent;
